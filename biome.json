{"$schema": "https://biomejs.dev/schemas/1.4.1/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off"}, "suspicious": {"noExplicitAny": "warn"}, "correctness": {"noUnusedVariables": "error"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "asNeeded", "bracketSpacing": true, "bracketSameLine": false, "quoteProperties": "asNeeded"}}, "json": {"formatter": {"enabled": true}}, "files": {"include": ["src/**/*.ts", "src/**/*.js", "src/**/*.json"], "ignore": ["node_modules", "dist", "build", ".git"]}}