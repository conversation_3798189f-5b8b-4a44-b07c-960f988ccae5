import { config } from "@/config";
import { logger } from "@/utils/logger";
import { BatchProcessor } from "./batch/BatchProcessor";

async function main() {
	try {
		logger.info("Starting batch processing system...", {
			nodeEnv: config.NODE_ENV,
			databaseUrl: config.DATABASE_URL ? "configured" : "missing",
		});

		const processor = new BatchProcessor();
		await processor.initialize();

		// Get job name from command line arguments
		const jobName = process.argv[2];
		if (!jobName) {
			logger.error("Job name is required. Usage: npm start <job-name>");
			process.exit(1);
		}

		logger.info(`Executing job: ${jobName}`);
		await processor.executeJob(jobName);

		logger.info("Batch processing completed successfully");
		process.exit(0);
	} catch (error) {
		logger.error("Batch processing failed", { error });
		process.exit(1);
	}
}

// Handle process termination gracefully
process.on("SIGTERM", () => {
	logger.info("Received SIGTERM, shutting down gracefully");
	process.exit(0);
});

process.on("SIGINT", () => {
	logger.info("Received SIGINT, shutting down gracefully");
	process.exit(0);
});

main().catch((error) => {
	logger.error("Unhandled error in main process", { error });
	process.exit(1);
});
