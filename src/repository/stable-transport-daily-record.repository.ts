import { PrismaClient, type StableTmTransportDailyRecord } from '@prisma/client';
import type { Result } from 'neverthrow';
import { err, ok } from 'neverthrow';
import { DatabaseError } from './database-error';

export const getUnconfirmedRecordsBeforeDate = async (
  date: Date,
  prisma?: PrismaClient,
): Promise<Result<StableTmTransportDailyRecord[], DatabaseError>> => {
  const client = prisma || new PrismaClient();

  try {
    const targetYear = date.getFullYear();
    const targetMonth = date.getMonth() + 1; // JavaScript months are 0-indexed
    const targetDay = date.getDate();

    const records = await client.stableTmTransportDailyRecord.findMany({
      where: {
        isConfirmed: false,
        OR: [
          {
            year: {
              lt: targetYear,
            },
          },
          {
            year: targetYear,
            month: {
              lt: targetMonth,
            },
          },
          {
            year: targetYear,
            month: targetMonth,
            day: {
              lt: targetDay,
            },
          },
        ],
      },
      orderBy: [{ year: 'asc' }, { month: 'asc' }, { day: 'asc' }],
    });

    return ok(records);
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown database error';
    return err(new DatabaseError(`Failed to fetch unconfirmed records: ${message}`));
  } finally {
    if (!prisma) {
      await client.$disconnect();
    }
  }
};
